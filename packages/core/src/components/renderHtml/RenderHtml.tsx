'use client'

import { useEffect, useMemo, useRef } from 'react'

import { useScaleImageUrl } from '../../businessHooks'

import './style.css'

/**
 * 渲染 HTML（全站统一隐藏 404 图片）
 * @param {object} props - 组件参数
 * @param {string} props.content - HTML 内容
 * @param {number} props.width - 宽度
 * @param {number} props.center - 居中
 */
const RenderHtml = ({
  content,
  width,
  center,
  contentStyle = '',
}: {
  content: string
  width?: number
  center?: boolean
  contentStyle?: string
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const scaleImageUrl = useScaleImageUrl()

  console.log('====', content, width, center, contentStyle)

  // 预处理HTML内容，优化图片URL
  const optimizedContent = useMemo(() => {
    if (!content) return content

    // 使用正则表达式匹配img标签的src属性
    const imgRegex = /<img([^>]*?)src=["']([^"']*?)["']([^>]*?)>/gi

    return content.replace(imgRegex, (match, beforeSrc, srcUrl, afterSrc) => {
      // 获取优化后的URL，使用传入的width或默认375
      const optimizedSrc = scaleImageUrl(srcUrl, width || 375)
      console.log('预处理优化图片:', srcUrl, '->', optimizedSrc)

      // 如果URL有变化，则替换
      if (optimizedSrc !== srcUrl) {
        return `<img${beforeSrc}src="${optimizedSrc}"${afterSrc}>`
      }
      return match
    })
  }, [content, scaleImageUrl, width])

  useEffect(() => {
    const el = containerRef.current
    if (!el) return

    const hide = (img: HTMLImageElement) => {
      img.style.display = 'none'
    }

    const onErr = (e: Event) => hide(e.target as HTMLImageElement)

    const optimizeImage = (img: HTMLImageElement) => {
      const originalSrc = img.src
      if (originalSrc) {
        // 获取图片的显示宽度，如果没有则使用默认值375
        const imgWidth = img.width || img.offsetWidth || width || 375
        const optimizedSrc = scaleImageUrl(originalSrc, imgWidth)

        console.log('优化图片:', originalSrc, '->', optimizedSrc, '宽度:', imgWidth)

        // 只有当优化后的URL与原URL不同时才更新
        if (optimizedSrc !== originalSrc) {
          img.src = optimizedSrc
        }
      }
    }

    const watch = (img: HTMLImageElement) => {
      img.addEventListener('error', onErr, { passive: true })
      if (img.complete) {
        if (img.naturalWidth === 0) hide(img)
        else optimizeImage(img) // 图片已加载完成，立即优化
      } else {
        const onLoad = () => {
          if (img.naturalWidth === 0) hide(img)
          else optimizeImage(img) // 图片加载完成后优化
        }
        img.addEventListener('load', onLoad, { passive: true, once: true })
      }
    }

    const scan = () => {
      const images = el.querySelectorAll('img')
      console.log('扫描到图片数量:', images.length)
      images.forEach((img) => watch(img as HTMLImageElement))
    }

    // 延迟扫描，确保DOM已经渲染完成
    const timeoutId = setTimeout(() => {
      scan()
    }, 100)

    const mo = new MutationObserver(() => {
      console.log('DOM发生变化，重新扫描图片')
      scan()
    })
    mo.observe(el, { childList: true, subtree: true })

    return () => {
      clearTimeout(timeoutId)
      el.querySelectorAll('img').forEach((img) => img.removeEventListener('error', onErr))
      mo.disconnect()
    }
  }, [optimizedContent, scaleImageUrl, width])

  return (
    <div
      ref={containerRef}
      className={`${width ? `w-[${width}px]` : 'w-full'} ${center ? 'flex flex-col items-center' : ''} render-html-container ${contentStyle}`}
      dangerouslySetInnerHTML={{ __html: optimizedContent }}
    />
  )
}

export default RenderHtml

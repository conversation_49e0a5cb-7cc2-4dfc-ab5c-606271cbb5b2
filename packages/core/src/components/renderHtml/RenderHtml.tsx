'use client'

import { useEffect, useRef } from 'react'

import { useScaleImageUrl } from '../../businessHooks'
import './style.css'

/**
 * 渲染 HTML（全站统一隐藏 404 图片）
 * @param {object} props - 组件参数
 * @param {string} props.content - HTML 内容
 * @param {number} props.width - 宽度
 * @param {number} props.center - 居中
 */
const RenderHtml = ({
  content,
  width,
  center,
  contentStyle = '',
}: {
  content: string
  width?: number
  center?: boolean
  contentStyle?: string
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const scaleImageUrl = useScaleImageUrl()

  useEffect(() => {
    const el = containerRef.current
    if (!el) return

    const hide = (img: HTMLImageElement) => {
      img.style.display = 'none'
    }

    const onErr = (e: Event) => hide(e.target as HTMLImageElement)

    const optimizeImage = (img: HTMLImageElement) => {
      const originalSrc = img.src
      if (originalSrc) {
        // 获取图片的显示宽度，如果没有则使用默认值375
        const imgWidth = img.width || img.offsetWidth || 375
        const optimizedSrc = scaleImageUrl(originalSrc, imgWidth)

        // 只有当优化后的URL与原URL不同时才更新
        if (optimizedSrc !== originalSrc) {
          img.src = optimizedSrc
        }
      }
    }

    const watch = (img: HTMLImageElement) => {
      // 先尝试优化图片
      optimizeImage(img)

      img.addEventListener('error', onErr, { passive: true })
      if (img.complete) {
        if (img.naturalWidth === 0) hide(img)
      } else {
        const onLoad = () => {
          if (img.naturalWidth === 0) hide(img)
        }
        img.addEventListener('load', onLoad, { passive: true, once: true })
      }
    }

    const scan = () => {
      el.querySelectorAll('img').forEach((img) => watch(img as HTMLImageElement))
    }

    scan()

    const mo = new MutationObserver(() => scan())
    mo.observe(el, { childList: true, subtree: true })

    return () => {
      el.querySelectorAll('img').forEach((img) => img.removeEventListener('error', onErr))
      mo.disconnect()
    }
  }, [content, scaleImageUrl])

  return (
    <div
      ref={containerRef}
      className={`${width ? `w-[${width}px]` : 'w-full'} ${center ? 'flex flex-col items-center' : ''} render-html-container ${contentStyle}`}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  )
}

export default RenderHtml

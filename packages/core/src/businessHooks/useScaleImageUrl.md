# useScaleImageUrl Hook 使用文档

## 概述

`useScaleImageUrl` 是一个用于图片格式优化的自定义 React Hook，提供智能的图片 URL 处理功能，包括 WebP 格式转换、设备像素比适配和动态尺寸调整。

## 功能特性

- 🖼️ **WebP 格式转换**：自动将支持的图片转换为 WebP 格式，减少文件大小 25-35%
- 📱 **设备像素比适配**：根据设备像素比动态调整图片尺寸，确保高清屏显示清晰
- 🎯 **动态尺寸调整**：根据实际显示需求优化图片尺寸，避免加载过大图片
- 🔧 **URL 参数保留**：智能保留原有 URL 参数，确保兼容性
- ⚡ **性能优化**：使用 `useCallback` 优化性能，避免不必要的重新计算

## 安装和导入

```tsx
import { useScaleImageUrl } from '@ninebot/core'
```

## 基础用法

### 1. 基本使用

```tsx
import React from 'react'
import { useScaleImageUrl } from '@ninebot/core'

const MyComponent = () => {
  const scaleImageUrl = useScaleImageUrl()

  const imageUrl = 'https://example.com/image-webp.webp'
  const optimizedUrl = scaleImageUrl(imageUrl, 200)

  return <img src={optimizedUrl} alt="优化后的图片" />
}
```

### 2. 在 CustomImage 组件中使用

```tsx
import { CustomImage } from '@/components/common'

const ProductCard = () => {
  return (
    <CustomImage
      src="https://example.com/product-webp.webp"
      alt="商品图片"
      width={300}
      height={300}
      enableImageOptimization={true} // 启用图片优化
    />
  )
}
```

### 3. 响应式图片优化

```tsx
const ResponsiveImage = () => {
  const scaleImageUrl = useScaleImageUrl()

  // 根据屏幕尺寸动态调整
  const screenWidth = typeof window !== 'undefined' ? window.innerWidth : 375
  const optimizedUrl = scaleImageUrl(imageUrl, screenWidth)

  return <img src={optimizedUrl} alt="响应式图片" style={{ width: '100%', height: 'auto' }} />
}
```

## API 参考

### useScaleImageUrl()

返回一个图片 URL 优化函数。

**返回值：**

- `scaleImageUrl: (url: string, width?: number) => string`

### scaleImageUrl(url, width)

处理图片 URL 的缩放和格式优化。

**参数：**

- `url` (string): 图片 URL，支持包含 '-webp.webp' 的 URL
- `width` (number, 可选): 图片宽度，默认为 375

**返回值：**

- `string`: 处理后的图片 URL

## 配置说明

Hook 依赖于 Redux store 中的 `storeConfig` 配置：

```typescript
interface StoreConfig {
  webp?: string // WebP 标识符，默认: '-webp.webp'
  webpformat?: string // WebP 格式模板，默认: 'imageView2/2/w/{width}/format/webp/q/95'
}
```

### 默认配置

```typescript
const defaultConfig = {
  webp: '-webp.webp',
  webpformat: 'imageView2/2/w/{width}/format/webp/q/95',
}
```

## 处理流程

1. **URL 检测**：检查图片 URL 是否包含 WebP 标识符
2. **设备适配**：获取设备像素比并计算实际宽度
3. **参数解析**：使用正则表达式安全地解析 URL 参数
4. **格式转换**：应用 WebP 压缩和质量优化
5. **URL 重构**：生成优化后的图片 URL，保留原有参数

## 使用场景

### 商品展示

```tsx
const ProductGallery = ({ images }) => {
  const scaleImageUrl = useScaleImageUrl()

  return (
    <div className="product-gallery">
      {images.map((image, index) => (
        <img key={index} src={scaleImageUrl(image.url, 400)} alt={`商品图片 ${index + 1}`} />
      ))}
    </div>
  )
}
```

### 用户头像

```tsx
const UserAvatar = ({ avatarUrl, size = 64 }) => {
  const scaleImageUrl = useScaleImageUrl()

  return (
    <img
      src={scaleImageUrl(avatarUrl, size)}
      alt="用户头像"
      className="rounded-full"
      width={size}
      height={size}
    />
  )
}
```

### 轮播图

```tsx
const Carousel = ({ banners }) => {
  const scaleImageUrl = useScaleImageUrl()
  const screenWidth = typeof window !== 'undefined' ? window.innerWidth : 375

  return (
    <div className="carousel">
      {banners.map((banner) => (
        <img key={banner.id} src={scaleImageUrl(banner.imageUrl, screenWidth)} alt={banner.title} />
      ))}
    </div>
  )
}
```

## 性能优化建议

1. **合理设置宽度**：根据实际显示尺寸传入合适的 width 参数
2. **避免过度优化**：对于小图片或已经优化的图片，可以禁用优化
3. **缓存策略**：配合图片缓存机制使用，提升加载性能

## 注意事项

1. **URL 格式要求**：只处理包含 WebP 标识符的 URL
2. **参数保留**：会保留原有 URL 参数的完整性
3. **设备兼容性**：在服务端渲染时会使用默认像素比
4. **错误处理**：对于无效 URL 会返回原始 URL

## 故障排除

### 图片没有被优化

检查以下几点：

1. 图片 URL 是否包含配置的 WebP 标识符
2. `storeConfig` 是否正确加载
3. `enableImageOptimization` 是否设置为 true

### 图片显示模糊

可能的原因：

1. 传入的 width 参数过小
2. 设备像素比计算不正确
3. 图片服务器不支持指定的尺寸

### 性能问题

优化建议：

1. 避免在渲染循环中重复调用
2. 使用合适的图片尺寸
3. 配合图片懒加载使用

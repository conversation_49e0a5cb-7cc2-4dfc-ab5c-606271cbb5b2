'use client'
import clsx from 'clsx'

import { CustomImage } from '@/components/common'
import { SoldOutTag } from '@/components/icons'
import type { ProductOptionItem, ProductStatus, Variant } from '@/types/product'

interface ImageCompProps {
  id: string
  variants: Variant[]
}

interface ColorSelectorProps {
  id: string
  optionItems: ProductOptionItem[]
  variants: Variant[]
  productStatus: ProductStatus
  onSelectionChange: (item: ProductOptionItem, id: string) => void
}

const ImageComp: React.FC<ImageCompProps> = ({ id, variants }) => {
  const tempConfigUid: string[] = []
  let url = ''
  let label = ''

  variants.forEach((variant) => {
    if (
      variant.attributes &&
      variant.attributes.find((attr) => attr.code.includes('color'))?.uid === id &&
      !tempConfigUid.includes(id)
    ) {
      tempConfigUid.push(id)
      url = variant.product.image.url
      label = variant.product.name
    }
  })

  return url ? (
    <CustomImage
      src={url}
      alt={label || ''}
      className="object-contain"
      width="100%"
      height="100%"
      optimizationWidth={103}
    />
  ) : null
}

const ColorSelector: React.FC<ColorSelectorProps> = ({
  id,
  optionItems,
  variants,
  productStatus,
  onSelectionChange,
}) => {
  const { optionSelections } = productStatus
  const optionId = optionSelections?.get(id)
  const { outOfStockVariants, isEverythingOutOfStock } = productStatus

  // 对选项进行排序，将售罄的选项移动到末尾
  const sortedOptionItems = [...optionItems].sort((a, b) => {
    let isAOutOfStock = false
    let isBOutOfStock = false

    if (outOfStockVariants && outOfStockVariants.length > 0) {
      const flatOutOfStockArray = outOfStockVariants.flat()
      isAOutOfStock = flatOutOfStockArray.includes(a.value_index)
      isBOutOfStock = flatOutOfStockArray.includes(b.value_index)
    }

    // 如果A售罄但B没有售罄，A排在后面
    if (isAOutOfStock && !isBOutOfStock) return 1
    // 如果B售罄但A没有售罄，B排在后面
    if (!isAOutOfStock && isBOutOfStock) return -1
    // 其他情况保持原有顺序
    return 0
  })

  return (
    //
    <div className="grid grid-cols-3 gap-x-[6px] gap-y-[8px] pb-2">
      {sortedOptionItems.map((item) => {
        let isOptionOutOfStock = false
        if (outOfStockVariants && outOfStockVariants.length > 0) {
          const flatOutOfStockArray = outOfStockVariants.flat()
          isOptionOutOfStock = flatOutOfStockArray.includes(item.value_index)
        }
        return (
          <div
            key={item.uid}
            className={clsx(
              'relative rounded-base border',
              optionId === item.value_index
                ? 'overflow-hidden border-primary bg-[#DA291C0D] text-primary'
                : 'border-[#F4F4F4] bg-[#F4F4F4]',
              isOptionOutOfStock && 'cursor-not-allowed',
            )}>
            <div
              className={clsx('box-content w-[102px]', isOptionOutOfStock && 'opacity-50')}
              onClick={() => {
                if (
                  !(isEverythingOutOfStock || isOptionOutOfStock) &&
                  optionId !== item.value_index
                ) {
                  onSelectionChange(item, id)
                }
              }}>
              <div
                className={clsx(
                  'relative h-[103px] w-[103px] overflow-hidden rounded-t-base bg-[#F4F4F4]',
                  isOptionOutOfStock && 'opacity-50',
                )}>
                <ImageComp id={item.uid || ''} variants={variants} />
              </div>
              <div className="flex h-[40px] items-center justify-center">
                <div
                  className={clsx(
                    'my-base line-clamp-2 px-1 text-center text-base',
                    optionId === item.value_index ? 'text-primary' : 'text-black',
                  )}>
                  {item.label}
                </div>
              </div>
            </div>
            {isOptionOutOfStock && (
              <>
                <div className="absolute inset-0 rounded-base bg-[#0000000D]" />
                <div className="absolute -top-2 right-0">
                  <SoldOutTag />
                </div>
              </>
            )}
          </div>
        )
      })}
    </div>
  )
}

export default ColorSelector

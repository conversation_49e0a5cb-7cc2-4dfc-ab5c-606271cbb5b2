'use client'
import React, { useCallback, useMemo } from 'react'
import { useTranslations } from 'next-intl'
import {
  convertTimeToTimestamp,
  generateEventParams,
  mergeStyles,
  NCoinRange,
  PriceRange,
  type PriceRanges,
  TRACK_EVENT,
  URL_TYPE,
  useVolcAnalytics,
} from '@ninebot/core'
import { HomeProduct } from '@ninebot/core'

import { CustomImage } from '@/components/common'
import { Link } from '@/i18n/navigation'

import styles from './styles'

/**
 * 产品 Card 组件
 * @param {Object} props - 组件参数。
 * @param {Object} props.product - 产品的数据对象。
 * @param {string} props.product.name - 产品的名称。
 * @param {string} props.product.sku - 产品的 SKU。
 * @param {Object} props.product.image - 产品的图片 对象。
 * @param {string} props.product.image.url - 产品的图片 URL。
 * @param {string} props.product.image.label - 产品的图片 label。
 * @param {Object} props.product.price_range - 产品的价格范围。
 * @param {string} props.product.special_to_date_timestamp - 产品的促销价结束日期。
 * @param {Object} props.product.custom_attributesV3 - 产品的自定义属性。
 * @param {Object} props.containerStyle - 自定义产品容器样式。
 * @param {Object} props.imageStyle - 自定义产品图片样式。
 * @param {Object} props.nameWrapperStyle - 自定义产品名称容器样式。
 * @param {string} props.sku - 产品的 SKU。
 * @param {Function} props.openPage - 打开页面的函数。
 * @param {string} props.currentTime - 当前时间。
 *
 * @returns {JSX.Element} 产品card 组件
 */

type ProductCardProps = {
  product: HomeProduct
  currentTime: string
  containerStyle?: string
  imageStyle?: React.CSSProperties & Partial<Record<'--width' | '--height', string>>
  nameWrapperStyle?: string
  pageType?: 'home' | 'category' | 'sort' | 'search' | 'searchResult' | 'homeDiscount' | 'cart' // 页面类型，用于区分埋点事件
}

const ProductCard = ({
  product,
  currentTime,
  containerStyle = '',
  imageStyle = {},
  nameWrapperStyle = '',
  pageType = 'home',
}: ProductCardProps) => {
  const {
    __typename: producType,
    name,
    image,
    price_range: priceRange,
    sku,
    variants = [],
    special_to_date_timestamp: specialToDate = '',
    new_to_date: newToDate = '',
    stock_status: stockStatus,
  } = product

  const getI18nString = useTranslations('Common')
  const customAttributes = useMemo(() => product?.custom_attributesV3?.items, [product])
  const { reportEvent } = useVolcAnalytics()

  const isDiscount = useMemo(() => {
    if (producType === 'ConfigurableProduct' && variants.length > 0) {
      return variants[0].product.special_to_date_timestamp > currentTime
    }

    return Number(specialToDate) > Number(currentTime)
  }, [producType, variants, currentTime, specialToDate])

  const priceRanges = useMemo(() => {
    let currentPriceRange = priceRange
    if (producType === 'ConfigurableProduct' && variants.length > 0) {
      currentPriceRange = variants[0].product.price_range
    }

    if (isDiscount) {
      return currentPriceRange
    }

    return {
      maximum_price: {
        regular_price: currentPriceRange?.maximum_price?.regular_price,
        final_price: currentPriceRange?.maximum_price?.regular_price,
        discount: {
          amount_off: 0,
        },
      },
    }
  }, [producType, variants, priceRange, isDiscount])

  // const isInsurance = useMemo(() => {
  //   const insurance = customAttributes?.find(
  //     (item: { code: string }) => item?.code === 'is_insurance',
  //   )
  //   if (insurance && 'value' in insurance) {
  //     return insurance.value === '1'
  //   }

  //   return false
  // }, [customAttributes])

  // const insuranceLink = useMemo(() => {
  //   const insuranceLink = customAttributes?.find(
  //     (item: { code: string }) => item?.code === 'insurance_link',
  //   )
  //   if (insuranceLink && 'value' in insuranceLink) {
  //     return insuranceLink.value
  //   }
  // }, [customAttributes])

  const handlePress = useCallback(() => {
    // let params = {
    //   type: URL_TYPE.product,
    //   value: sku || '',
    //   url: `/${product?.url_key}${product?.url_suffix}`,
    // }

    // 根据页面类型选择不同的埋点事件
    let trackEvent = TRACK_EVENT.shop_homepage_category_product_picture_click
    switch (pageType) {
      case 'home':
        trackEvent = TRACK_EVENT.shop_homepage_category_product_picture_click
        break
      case 'homeDiscount':
        trackEvent = TRACK_EVENT.shop_homepage_discount_product_picture_click
        break
      case 'category':
        trackEvent = TRACK_EVENT.shop_category_product_picture_click
        break
      case 'sort':
        trackEvent = TRACK_EVENT.shop_sort_product_picture_click
        break
      case 'search':
        trackEvent = TRACK_EVENT.shop_searchpage_product_picture_click
        break
      case 'searchResult':
        trackEvent = TRACK_EVENT.shop_searchresult_product_picture_click
        break
      case 'cart':
        trackEvent = TRACK_EVENT.shop_cart_product_picture_click
        break
      default:
        trackEvent = TRACK_EVENT.shop_homepage_category_product_picture_click
    }

    reportEvent(
      trackEvent,
      generateEventParams({
        type: URL_TYPE.product,
        value: sku || '',
        label: name || '',
      }),
    )

    // if (isInsurance && insuranceLink) {
    //   params = {
    //     type: URL_TYPE.custom,
    //     value: insuranceLink,
    //     url: insuranceLink,
    //   }
    // }
  }, [sku, reportEvent, pageType, name])

  const type = useMemo(() => {
    const option = customAttributes?.find((item: { code: string }) => item?.code === 'product_tag')
    if (option && 'selected_options' in option) {
      return option.selected_options?.[0]?.label
    }
  }, [customAttributes])

  const showNcoin = useMemo(() => {
    if (producType === 'ConfigurableProduct' && variants.length > 0) {
      return variants[0].product.paymeng_method === 'payment_method_ncoin'
    }

    const option = customAttributes?.find(
      (item: { code: string }) => item?.code === 'paymeng_method',
    )

    if (option && 'selected_options' in option) {
      return option.selected_options?.[0]?.value === 'payment_method_ncoin'
    }

    return false
  }, [producType, variants, customAttributes])

  const isInStock = useMemo(() => {
    if (producType === 'ConfigurableProduct' && variants?.length > 0) {
      return variants?.some(
        (variant: { product: { stock_status: string } }) =>
          variant?.product?.stock_status === 'IN_STOCK',
      )
    }

    return stockStatus === 'IN_STOCK'
  }, [producType, variants, stockStatus])

  const showTag = useMemo(() => {
    return convertTimeToTimestamp(newToDate || '') > Number(currentTime)
  }, [currentTime, newToDate])

  return (
    <Link
      className={mergeStyles([styles.container, 'block', containerStyle])}
      style={styles.containerStyle}
      onClick={handlePress}
      href={`/${product?.url_key}${product?.url_suffix}`}>
      <div className="relative">
        <CustomImage
          style={{
            ...styles.image,
            ...(['home', 'category', 'searchResult', 'sort', 'homeDiscount'].includes(pageType) && {
              backgroundColor: '#F3F3F4',
            }),
            ...imageStyle,
          }}
          src={image?.url || ''}
          alt={name || ''}
          optimizationWidth={180}
        />
        {showTag && (
          <div className={styles.tagWrapper}>
            <div className={styles.tag}>{getI18nString('product_tag')}</div>
          </div>
        )}
        {!isInStock && (
          <div className={styles.status}>
            <div className={styles.statusText}>{getI18nString('out_of_stock_tip')}</div>
          </div>
        )}
      </div>
      <div className={styles.text} style={styles.textStyle}>
        <div className={styles.typeWrapper}>
          <div className={styles.type}>{type}</div>
        </div>
        <div className={mergeStyles([styles.nameWrapper, nameWrapperStyle])}>
          <div className={styles.name}>{name}</div>
        </div>
        <div className={styles.flexRowCenter}>
          {showNcoin ? (
            <NCoinRange
              iconStyle={{ size: 14 }}
              priceRange={priceRanges as PriceRanges}
              textStyle={styles.customPriceText}
              originTextStyle={styles.customUnderLineText}
            />
          ) : (
            <PriceRange
              priceRange={priceRanges as PriceRanges}
              textStyle={styles.customPriceText}
            />
          )}
        </div>
      </div>
    </Link>
  )
}

export default ProductCard

'use client'

import React, { useCallback, useState } from 'react'
import NextImage from 'next/image'
/**
 * 默认占位符图片路径（使用本地图片资源）
 */
import DEFAULT_PLACEHOLDER from '@public/images/default.png'
import { useScaleImageUrl } from '@ninebot/core'
import { Image, type ImageProps } from 'antd-mobile'
export interface CustomImageProps extends ImageProps {
  /**
   * 自定义默认图片URL，如果不传则使用全局默认图片
   */
  defaultSrc?: string
  /**
   * 是否启用默认图片功能，默认为true
   */
  enableDefaultImage?: boolean
  /**
   * 是否在加载时也显示默认图片作为占位符，默认为true
   */
  showDefaultOnLoading?: boolean
  /**
   * 图片加载失败时的回调
   */
  onError?: (event: React.SyntheticEvent<HTMLImageElement, Event>) => void
  /**
   * 是否启用图片优化功能，默认为true
   */
  enableImageOptimization?: boolean
  /**
   * 图片优化的宽度，用于计算缩放后的尺寸，默认为375
   */
  optimizationWidth?: number
}

/**
 * 自定义Image组件
 *
 * 基于antd-mobile的Image组件封装，增加了默认图片功能
 * 当图片加载失败时，会自动显示默认图片
 *
 * @param props - 组件属性
 * @returns React组件
 */
const CustomImage: React.FC<CustomImageProps> = ({
  src,
  defaultSrc = DEFAULT_PLACEHOLDER.src,
  enableDefaultImage = true,
  showDefaultOnLoading = true,
  enableImageOptimization = true,
  optimizationWidth = 375,
  onError,
  ...rest
}) => {
  const scaleImageUrl = useScaleImageUrl()
  const [currentSrc, setCurrentSrc] = useState<string>(src || '')
  const [hasError, setHasError] = useState<boolean>(false)

  // 获取优化后的图片URL
  const getOptimizedSrc = useCallback(
    (url: string) => {
      if (!enableImageOptimization || !url) {
        return url
      }
      return scaleImageUrl(url, optimizationWidth)
    },
    [enableImageOptimization, scaleImageUrl, optimizationWidth],
  )

  const handleError = useCallback(
    (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
      // 如果启用默认图片功能且当前不是默认图片且还没有出错过
      if (enableDefaultImage && currentSrc !== defaultSrc && !hasError) {
        setCurrentSrc(defaultSrc)
        setHasError(true)
      }

      // 调用外部传入的onError回调
      onError?.(event)
    },
    [enableDefaultImage, currentSrc, defaultSrc, hasError, onError],
  )

  // 当src属性变化时，重置状态并应用图片优化
  React.useEffect(() => {
    const optimizedSrc = getOptimizedSrc(src || '')
    if (optimizedSrc !== currentSrc && !hasError) {
      setCurrentSrc(optimizedSrc)
      setHasError(false)
    }
  }, [src, currentSrc, hasError, getOptimizedSrc])

  return (
    <Image
      {...rest}
      alt={rest.alt || ''}
      src={currentSrc}
      onError={handleError}
      placeholder={
        enableDefaultImage && showDefaultOnLoading ? (
          <div style={{ width: '100%', height: '100%', position: 'relative', overflow: 'hidden' }}>
            <NextImage
              src={defaultSrc}
              alt=""
              width={400}
              height={400}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
              }}
              priority={false}
              unoptimized={true}
            />
          </div>
        ) : (
          rest.placeholder
        )
      }
    />
  )
}

export default CustomImage

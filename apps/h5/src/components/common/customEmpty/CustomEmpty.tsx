'use client'
import React from 'react'
import { useTranslations } from 'next-intl'
import empty from '@public/images/empty.png'
import { generateOSSUrl, mergeStyles } from '@ninebot/core'

import CustomImage from '../CustomImage'

import styles from './styles'

/**
 * Empty 组件
 *
 * 用于在页面或列表没有内容时，显示占位的图片和描述文字，并支持自定义内容。
 *
 * @component
 * @param {Object} props 组件的属性
 * @param {number} [props.imageWidth=108] 占位图片的宽度，默认值为 108。
 * @param {number} [props.imageHeight=108] 占位图片的高度，默认值为 108。
 * @param {Object} [props.source=require('../../assets/images/empty.png')] 占位图片的资源路径，默认使用内置图片。
 * @param {string} [props.description='当前页面什么也没有'] 描述文字，默认为“当前页面什么也没有”。
 * @param {Object} [props.style] 自定义的根容器样式，用于覆盖或合并默认样式。
 * @param {Object} [props.descriptionStyle] 自定义的描述文字样式，用于覆盖或合并默认样式。
 * @param {React.ReactNode} [props.children] 可选的子组件，用于在占位组件中添加自定义内容。
 *
 * @returns {React.ReactNode} Empty 组件。
 */

type EmptyProps = {
  imageWidth?: number
  imageHeight?: number
  source?: string
  description?: string
  style?: string
  descriptionStyle?: string
  children?: React.ReactNode
}

const Empty = (props: EmptyProps) => {
  const getI18nString = useTranslations('Common')
  const {
    imageWidth = 108,
    imageHeight = 108,
    source,
    description = getI18nString('empty_page'),
    style = '',
    descriptionStyle = '',
    children,
  } = props

  return (
    <div className={mergeStyles([styles.root, style])}>
      <CustomImage
        style={{ width: imageWidth, height: imageHeight }}
        src={source ? source : generateOSSUrl('/images/empty.png') || empty.src}
        alt="empty"
        enableDefaultImage={false}
        enableImageOptimization={false}
      />
      {description ? (
        <div className={mergeStyles(styles.description, descriptionStyle)}>{description}</div>
      ) : null}
      {children}
    </div>
  )
}

export default Empty

'use client'
import React, { useCallback } from 'react'
import { mergeStyles, RelationItem, useScaleImageUrl } from '@ninebot/core'
import { Image } from 'antd-mobile'

import { Arrow } from '@/components'
import { Link } from '@/i18n/navigation'

import styles from './styles'

/**
 * @description 首页活动模块
 * @param {object} props - 组件参数
 * @param {Object[]} [props.activities] - 活动模块列表，每个活动包含相关的配置。
 * @param {Object} [props.activities[].button_url] - 按钮的 URL 配置。
 * @param {string} [props.activities[].button_url.type] - 按钮 URL 的类型，例如 "CUSTOM"。
 * @param {string} [props.activities[].button_url.url] - 按钮的 URL 地址。
 * @param {string} [props.activities[].button_url.value] - 按钮的值，默认值为""。
 * @param {boolean} [props.activities[].button_arrow] - 是否显示按钮箭头，默认值为 `false`。
 * @param {string} [props.activities[].button_text] - 按钮的显示文字，默认为空字符串。
 * @param {string} [props.activities[].description] - 活动的描述信息。
 * @param {number} [props.activities[].id] - 活动的唯一标识符。
 * @param {string} [props.activities[].image_url] - 活动的图片 URL。
 * @param {string} [props.activities[].subtitle] - 活动的副标题。
 * @param {string} [props.activities[].title] - 活动的主标题。
 * @param {string} [props.activities[].video_url] - 活动的视频 URL。
 * @param {function} [props.openPage] - 跳转url的函数
 * @param {number} [props.bannerDisplayMode] - 显示模式，默认值为 2, 1 为纵向，2 为横向。
 *
 */

type ActivityBannerProps = {
  activities: RelationItem[]
  bannerDisplayMode: number
}

const ActivityBanner = ({ activities, bannerDisplayMode }: ActivityBannerProps) => {
  const scaleImageUrl = useScaleImageUrl()

  const handlePress = useCallback(() => {
    // TODO: 事件上报
  }, [])

  const renderHeaderItem = useCallback(
    (item: RelationItem) =>
      // 排除秒杀类型的组件
      item?.button_url?.type !== 'seckill' && (
        <Link
          className="relative"
          key={item?.id}
          onClick={(e) => {
            if (!item?.button_url?.url) {
              e.preventDefault()
              return
            }
            handlePress()
          }}
          href={item?.button_url?.url || '#'}>
          <Image
            style={{
              borderRadius: 12,
              overflow: 'hidden',
            }}
            src={scaleImageUrl(item?.image_url || '', 375)}
            fit="cover"
            alt={item?.title || '活动Banner图片'}
            width="100%"
            height="14rem"
          />
          <div className={styles.wrapper}>
            <div className={styles.header}>
              <div className={styles.headerTitle}>{item?.title}</div>
              <div className={styles.headerDescription}>
                <div className={styles.headerDescriptionText}>
                  {item?.subtitle || item?.button_text}
                </div>
                {item?.button_arrow && <Arrow color="#000000" />}
              </div>
            </div>
          </div>
        </Link>
      ),
    [handlePress, scaleImageUrl],
  )

  const renderItem = useCallback(
    (item: RelationItem, index: number) =>
      // 排除秒杀类型的组件
      item?.button_url?.type !== 'seckill' && (
        <Link
          className={mergeStyles([
            'relative',
            activities.length > 3 && index > 1 ? styles.listItem : '',
          ])}
          href={item?.button_url?.url || '#'}
          onClick={(e) => {
            if (!item?.button_url?.url) {
              e.preventDefault()
              return
            }
          }}>
          <Image
            style={{
              borderRadius: 12,
              overflow: 'hidden',
            }}
            src={scaleImageUrl(item?.image_url || '', 375)}
            fit="cover"
            alt={item?.title || '活动Banner图片'}
            width="100%"
            height="14rem"
          />
          <div className={styles.itemWrapper}>
            <div className={styles.header}>
              <div className={styles.headerTitle}>{item?.title}</div>
              <div className={styles.headerDescription}>
                <div className={styles.headerDescriptionText}>
                  {item?.subtitle ? item?.subtitle : item?.button_text}
                </div>
                {item?.button_arrow && <Arrow color="#000000" />}
              </div>
            </div>
          </div>
        </Link>
      ),
    [activities, scaleImageUrl],
  )

  const renderActivity = useCallback(() => {
    if (bannerDisplayMode === 1) {
      return activities.map((item) => (
        <div key={item?.id} className={styles.listItem}>
          {renderHeaderItem(item)}
        </div>
      ))
    }

    if (activities.length === 1) {
      return <div className={styles.topBanner}>{renderHeaderItem(activities[0])}</div>
    }

    if (activities.length === 3) {
      return (
        <div>
          <div className={styles.listHeader}>{renderHeaderItem(activities[0])}</div>
          <div className="mt-[6px] flex justify-between gap-[6px]">
            {activities.slice(1).map((item, index) => (
              <div key={item?.id} className={styles.listItem}>
                {renderItem(item, index)}
              </div>
            ))}
          </div>
        </div>
      )
    }

    return (
      <div className={mergeStyles([styles.list, styles.listMargin])}>
        {activities.map((item, index) => (
          <div key={item?.id} className={styles.listItem}>
            {renderItem(item, index)}
          </div>
        ))}
      </div>
    )
  }, [activities, renderHeaderItem, renderItem, bannerDisplayMode])

  if (!activities || activities.length === 0) {
    return null
  }

  return <div className={styles.container}>{renderActivity()}</div>
}

export default ActivityBanner

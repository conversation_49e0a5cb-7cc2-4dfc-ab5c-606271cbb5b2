'use client'
import {
  generateKingkongEventParams,
  RelationItem,
  TRACK_EVENT,
  useScaleImageUrl,
  useVolcAnalytics,
} from '@ninebot/core'
import { Image } from 'antd-mobile'

import { Link } from '@/i18n/navigation'

type QuickNavProps = {
  navItems: RelationItem[]
}

const QuickNav = ({ navItems }: QuickNavProps) => {
  const { reportEvent } = useVolcAnalytics()
  const scaleImageUrl = useScaleImageUrl()

  const handleKingkongClick = (item: RelationItem) => {
    // 埋点：生活页金刚区点击
    reportEvent(
      TRACK_EVENT.shop_life_kingkong_click,
      generateKingkongEventParams(item?.title || ''),
    )
  }

  return (
    <div className="flex flex-row items-center justify-between">
      {navItems.map((item) => (
        <Link
          key={item?.id}
          href={item?.button_url?.url || '#'}
          className="flex flex-col items-center justify-center"
          onClick={() => handleKingkongClick(item)}>
          <div className="h-16 w-16">
            <Image
              style={{
                width: 40,
                height: 40,
              }}
              src={scaleImageUrl(item?.image_url || '', 40)}
              alt={item?.title || '导航图片'}
            />
          </div>
          <div className="text-center text-[11px] leading-[15px] text-[#222223]">{item?.title}</div>
        </Link>
      ))}
    </div>
  )
}

export default QuickNav

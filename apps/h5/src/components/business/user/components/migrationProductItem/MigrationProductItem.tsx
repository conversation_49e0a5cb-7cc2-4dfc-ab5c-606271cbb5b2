import React from 'react'
import { NCoinView, useScaleImageUrl } from '@ninebot/core'
import { Image } from 'antd-mobile'

import styles from './styles'

/**
 * 迁移订单产品 Item
 */
const MigrationProductItem = ({
  productInfo,
  showPrice = true,
}: {
  productInfo: {
    product_material_code?: string | null
    product_need_npoint?: number | null
    product_qty?: number | null
    product_thumb_img?: string | null
    product_title?: string | null
  }
  showPrice?: boolean
}) => {
  const scaleImageUrl = useScaleImageUrl()

  return (
    <div className={styles.root}>
      <Image
        style={styles.image}
        src={scaleImageUrl(productInfo?.product_thumb_img || '', 88)}
        fit="cover"
        alt={productInfo?.product_title || ''}
      />

      <div className={styles.content}>
        <div className={styles.title}>
          <div className={styles.titleText}>{productInfo?.product_title}</div>
        </div>

        {showPrice && (
          <div className={styles.actions}>
            <NCoinView
              number={productInfo?.product_need_npoint || 0}
              textStyle={styles.nCoinText}
            />
            <div className={styles.qtyText}>×{productInfo?.product_qty}</div>
          </div>
        )}
      </div>
    </div>
  )
}

export default MigrationProductItem

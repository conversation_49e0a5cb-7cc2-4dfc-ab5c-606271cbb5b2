'use client'

import React, { useMemo } from 'react'
import { useTranslations } from 'next-intl'
import {
  generateOSSUrl,
  mergeStyles,
  NCoinView,
  OrderDetailItem,
  OrderReturnDetailItem,
  Price,
  useScaleImageUrl,
} from '@ninebot/core'
import { Image } from 'antd-mobile'

import { Arrow, CustomButton } from '@/components'
import { commonStyles } from '@/constants'

type ProductItemProps = {
  productInfo?: OrderDetailItem | OrderReturnDetailItem
  showAttr?: boolean
  showPrice?: boolean
  showQty?: boolean
  isReturn?: boolean
  setInstructionVisible?: () => void
  isShowInstruction?: boolean
}

const styles = {
  attrText: 'text-[12px] leading-[16px] text-[#6E6E73] font-miSansMedium380 truncate',
}

/**
 * 产品 Item
 */
const ProductItem = (props: ProductItemProps) => {
  const {
    productInfo,
    showAttr = true,
    showPrice = true,
    showQty = false,
    isReturn = false,
    setInstructionVisible,
    isShowInstruction = false,
  } = props

  const configOptions = useMemo(() => productInfo?.config_selected_options || [], [productInfo])
  const product = productInfo?.product
  const productSalePrice = productInfo?.product_sale_price

  const getI18nString = useTranslations('Common')
  const scaleImageUrl = useScaleImageUrl()

  const quantity = useMemo(() => {
    if (productInfo && 'qty' in productInfo) {
      return Number(productInfo.qty)
    }

    if (productInfo && 'quantity_ordered' in productInfo) {
      return Number(productInfo.quantity_ordered)
    }

    return 0
  }, [productInfo])

  /**
   * 是否自提产品
   */
  const isPickupProduct = useMemo(() => {
    if (productInfo && 'store_info' in productInfo) {
      return !!productInfo?.store_info?.store_name
    }

    return false
  }, [productInfo])

  /**
   * 是否虚拟产品
   */
  const isVirtualProduct = useMemo(() => {
    return productInfo?.product_type === 'virtual' || productInfo?.product_type === 'bundle'
  }, [productInfo?.product_type])

  /**
   * 是否纯N币兑换产品
   */
  const isNcoinProduct = useMemo(() => {
    return !!productInfo?.is_ncoin_pay
  }, [productInfo?.is_ncoin_pay])

  /**
   * 是否数字会员产品
   */
  const isMembershipProduct = useMemo(() => {
    if (productInfo && 'third_platform_member_code' in productInfo) {
      return Number(productInfo?.third_platform_member_code?.items?.length) > 0
    }
    return false
  }, [productInfo])

  /**
   * 是否是 Configurable 商品
   */
  const isConfigurableProduct = useMemo(() => {
    return configOptions.length > 0
  }, [configOptions])

  /**
   * configurable 商品的 option value
   */
  const configurableProductOptionValue = useMemo(() => {
    return isConfigurableProduct
      ? configOptions.map((option) => {
          return option?.value_label
        })
      : []
  }, [configOptions, isConfigurableProduct])

  if (!productInfo) return null

  return (
    <div className="flex flex-1 flex-row">
      {'requisition_status_label' in productInfo && productInfo?.requisition_status_label ? (
        <div className="relative h-[88px] w-[88px] overflow-hidden rounded-base bg-[#F8F8F9]">
          <Image
            src={scaleImageUrl(product?.image?.url || '', 88)}
            fit="cover"
            alt={product?.name || ''}
          />
          <div className="absolute bottom-0 flex h-8 w-full items-center justify-center bg-[#FEE5E5]">
            <span className="font-miSansRegular330 text-[12px] leading-[16px] text-primary">
              {productInfo?.requisition_status_label}
            </span>
          </div>
        </div>
      ) : (
        <Image
          style={{
            width: 88,
            height: 88,
            backgroundColor: '#F8F8F9',
            borderRadius: 8,
            overflow: 'hidden',
          }}
          src={scaleImageUrl(product?.image?.url || '', 88)}
          fit="cover"
          alt={product?.name || ''}
        />
      )}

      <div className="w-[60%] flex-1 pl-[12px]">
        <div className="mb-[8px] flex">
          {isPickupProduct ? (
            <div className="mr-[4px] mt-[2px] flex h-[16px] w-[16px] items-center justify-center rounded-[4px]">
              <Image
                style={{ width: 16, height: 16 }}
                src={generateOSSUrl('/icons/store.png')}
                fit="cover"
                alt=""
              />
            </div>
          ) : null}
          <div className="font-miSansMedium380 text-[14px] leading-[21px] text-[#0F0F0F]">
            {product?.name}
          </div>
        </div>

        {isShowInstruction && isVirtualProduct && !isReturn && !isMembershipProduct && (
          <CustomButton
            customStyle={{
              width: 'auto',
              padding: 0,
            }}
            onClick={setInstructionVisible}>
            <div className="flex flex-row items-center justify-center">
              <span className="font-miSansDemiBold450 text-[14px] leading-[19px] text-[#00000050]">
                {getI18nString('instructions')}
              </span>
              <Arrow color="rgba(0, 0, 0, 0.5)" size={14} rotate={0} />
            </div>
          </CustomButton>
        )}

        {showAttr &&
          !isVirtualProduct &&
          (isConfigurableProduct || isPickupProduct ? (
            <div className="flex flex-row items-center">
              <div className="mb-[8px] w-auto max-w-[227px] rounded-[4px] bg-[#F3F3F4] px-[8px] py-[4px]">
                <div className="flex flex-row items-center justify-start">
                  {isConfigurableProduct ? (
                    <div
                      className={mergeStyles([
                        styles.attrText,
                        isPickupProduct ? 'max-w-[50%]' : 'w-auto',
                      ])}>
                      {configurableProductOptionValue.join('，')}
                    </div>
                  ) : null}
                  {isPickupProduct ? (
                    <div className={mergeStyles([styles.attrText, 'mx-[4px]'])}>|</div>
                  ) : null}
                  {'store_info' in productInfo ? (
                    <div className={mergeStyles([styles.attrText, 'max-w-[60%]'])}>
                      {productInfo?.store_info?.store_name}
                    </div>
                  ) : null}
                </div>
              </div>
            </div>
          ) : null)}

        <div className={commonStyles.flex_row}>
          {showPrice &&
            !isVirtualProduct &&
            (isNcoinProduct ? (
              <NCoinView
                number={productSalePrice?.value || 0}
                textStyle="text-[16px] leading-[21px] text-[#000000] font-miSansDemiBold450"
              />
            ) : (
              productSalePrice && <Price bold price={productSalePrice} />
            ))}
          {showQty && !isVirtualProduct && (
            <div className="font-miSansRegular330 text-[12px] text-[#000000]">×{quantity}</div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ProductItem
